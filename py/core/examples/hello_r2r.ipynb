{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "\n", "from r2r import R2RClient\n", "\n", "# Create an account at SciPhi Cloud https://app.sciphi.ai and set an R2R_API_KEY environment variable\n", "# or set the base URL to your instance. E.g. R2RClient(\"http://localhost:7272\")\n", "os.environ[\"R2R_API_KEY\"] = \"your-api-key\"\n", "\n", "# Create a client\n", "client = R2RClient()"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'results': {'message': 'Ingest files task queued successfully.', 'task_id': 'd14004c5-09b7-4d15-acd6-6708ad394908', 'document_id': '********-0b1b-5459-a9e1-da0c781d5e71'}}\n"]}], "source": ["import os\n", "import tempfile\n", "\n", "import requests\n", "\n", "# Download the content from GitHub\n", "url = \"https://raw.githubusercontent.com/SciPhi-AI/R2R/refs/heads/main/py/core/examples/data/aristotle.txt\"\n", "response = requests.get(url)\n", "\n", "# Create a temporary file to store the content\n", "with tempfile.NamedTemporaryFile(\n", "    delete=False, mode=\"w\", suffix=\".txt\"\n", ") as temp_file:\n", "    temp_file.write(response.text)\n", "    temp_path = temp_file.name\n", "\n", "# Ingest the file\n", "ingestion_response = client.documents.create(file_path=temp_path)\n", "print(ingestion_response)\n", "\n", "# Clean up the temporary file\n", "os.unlink(temp_path)"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Performing RAG...\n", "The nature of the soul, according to <PERSON>, is multifaceted and can be understood through his three-part structure of the soul, which includes the vegetative soul, the sensitive soul, and the rational soul. Each type of soul has distinct functions:\n", "\n", "1. **Vegetative Soul**: This is concerned with growth and nourishment, and is present in all living beings, including plants [1], [2], [3].\n", "2. **Sensitive Soul**: This experiences sensations and movement, and is present in animals [1], [2], [3].\n", "3. **Rational Soul**: Unique to humans, this soul has the ability to receive forms of other things and to compare them using intellect (nous) and reason (logos) [1], [2], [3].\n", "\n", "For <PERSON>, the soul is the form of a living being, which means it is the essence that gives life to the body and enables it to perform its specific functions. The soul is what endows living beings with the ability to initiate movement, growth, and transformations [1], [2], [3]. <PERSON> also placed the rational soul in the heart, contrasting with earlier philosophers who located it in the brain [1], [2], [3].\n", "\n", "In contrast, the Hermetic perspective, as seen in the \"Corpus Hermeticum,\" views the soul as an immortal aspect of humanity that undergoes a transformative journey through various states of existence in pursuit of divine knowledge and enlightenment. The soul's journey emphasizes the importance of wisdom and virtue in achieving a higher understanding of existence and connecting with the divine [4], [5], [6], [7], [8], [9].\n", "\n", "Thus, the nature of the soul can be seen as both a vital essence that animates living beings and a divine entity that seeks knowledge and enlightenment through a transformative journey.\n"]}], "source": ["print(\"Performing RAG...\")\n", "rag_response = client.retrieval.rag(\n", "    query=\"What is the nature of the soul?\",\n", ")\n", "\n", "print(rag_response[\"results\"][\"completion\"])"]}], "metadata": {"kernelspec": {"display_name": "r2r-giROgG2W-py3.12", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 2}