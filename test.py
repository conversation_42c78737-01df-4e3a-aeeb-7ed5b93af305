from r2r import R2RClient

client = R2RClient(base_url="http://localhost:7272")

with open("test.txt", "w") as file:
    file.write("<PERSON> is a person that works at Google.")

# 上传文档
result = client.documents.create(file_path="test.txt")

# 执行基础搜索
search_results = client.retrieval.search(query="Who is <PERSON>?")

# 执行 RAG 查询
rag_response = client.retrieval.rag(
    query="Who is <PERSON>?",
    rag_generation_config={
        "model": "openai/gpt-4o-mini",
        "temperature": 0.0
    }
)

print(rag_response.results[0].completion)